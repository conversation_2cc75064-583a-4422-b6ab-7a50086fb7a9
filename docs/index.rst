.. Augmentor documentation master file, created by
   sphinx-quickstart on Thu Aug  4 13:26:55 2016.
   You can adapt this file completely to your liking, but it should at least
   contain the root `toctree` directive.

Augmentor
=========

Augmentor is a Python package designed to aid the augmentation and artificial generation of image data for machine learning tasks. It is primarily a data augmentation tool, but will also incorporate basic image pre-processing functionality.

.. tip::

    A Julia version of the package is also being actively developed. If you prefer to use Julia, you can find it `here <https://github.com/Evizero/Augmentor.jl>`_.

The documentation is organised as follows:

.. toctree::
    :maxdepth: 3
    :caption: User Guide

    userguide/mainfeatures
    userguide/install
    userguide/usage
    userguide/examples
    userguide/extend

    code

.. toctree::
    :maxdepth: 2
    :caption: Licence and Terms

    licence

Indices and tables
==================

* :ref:`genindex`
* :ref:`modindex`
* :ref:`search`
