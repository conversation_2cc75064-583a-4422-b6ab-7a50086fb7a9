# This workflow will install Python dependencies, run tests and lint

name: Pytest

on:
  push:
    branches: [ master ]
  pull_request:
    branches: [ master ]

jobs:
  build:

      runs-on: ${{ matrix.os }}
      strategy:
        matrix:
          os: [windows-latest, ubuntu-latest, macos-latest]  # windows-latest 
          python-version: [2.7, 3.5, 3.6, 3.7, 3.8, 3.9]

      steps:
      - uses: actions/checkout@v2
      - name: Set up Python ${{ matrix.python-version }}
        uses: actions/setup-python@v2
        with:
          python-version: ${{ matrix.python-version }}
      - name: Install dependencies
        run: |
          python -m pip install --upgrade pip
          pip install flake8 pytest
          pip install -r requirements.txt
          # if [ -f requirements.txt ]; then pip install -r requirements.txt; fi
      - name: Lint with flake8
        run: |
          # stop the build if there are Python syntax errors or undefined names
          flake8 . --count --select=E9,F63,F7,F82 --show-source --statistics
          # exit-zero treats all errors as warnings. The GitHub editor is 127 chars wide
          flake8 . --count --exit-zero --max-complexity=10 --max-line-length=127 --statistics
      - name: Test with pytest
        run: |
          pytest -v
