# This workflow will install the latest version of Augmentor using pip

name: PyPI Install

on:
  workflow_dispatch:
  release:

jobs:
  build:

    runs-on: ${{ matrix.os }}
    strategy:
      matrix:
        os: [ubuntu-latest, windows-latest, macos-latest]
        python-version: [2.7, 3.5, 3.6, 3.7, 3.8, 3.9]

    steps:
    # - uses: actions/checkout@v2
    - name: Set up Python ${{ matrix.python-version }}
      uses: actions/setup-python@v2
      with:
        python-version: ${{ matrix.python-version }}
    - name: Install Augmentor
      run: |
        python -m pip install --upgrade pip
        pip install Augmentor
    - name: Test that Augmentor can be imported and exit
      run: |
        python -c "import Augmentor"
