language: python
os:
  - linux
python:
  - "2.7"
  - "3.5"
  - "3.6"
  - "3.7"
# command to install dependencies
install:
  # Install conda.
  - if [[ "$TRAVIS_PYTHON_VERSION" == "2.7" ]]; then
      wget https://repo.continuum.io/miniconda/Miniconda2-latest-Linux-x86_64.sh -O miniconda.sh;
    else
      wget https://repo.continuum.io/miniconda/Miniconda3-latest-Linux-x86_64.sh -O miniconda.sh;
    fi
  - bash miniconda.sh -b -p $HOME/miniconda
  - export OPATH="$PATH" && export PATH="$HOME/miniconda/bin:$PATH"
  - conda create -q -y -n test-environment python=$TRAVIS_PYTHON_VERSION pytest
  - source activate test-environment
  # Attempt to install torchvision; on failure, revert back to pre-conda environment.
  - conda install -q -y torchvision -c soumith || export PATH="$OPATH"
  # Install pandas
  - conda install -q -y pandas
  - pip install -r requirements.txt
# command to run tests
script: py.test -v
